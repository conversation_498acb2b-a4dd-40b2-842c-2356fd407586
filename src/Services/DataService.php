<?php

namespace AlipayBillQuery\Services;

use AlipayBillQuery\Database\Connection;
use AlipayBillQuery\Models\ZfbData;
use Exception;

/**
 * 数据处理服务类
 */
class DataService
{
    private $db;
    private $zfbDataModel;

    public function __construct()
    {
        $this->db = Connection::getInstance();
        $this->zfbDataModel = new ZfbData();
    }

    /**
     * 记录同步日志
     */
    public function logSyncStart($syncType, $startTime, $endTime)
    {
        $sql = "INSERT INTO `zfb_sync_log` 
                (`sync_type`, `start_time`, `end_time`, `status`) 
                VALUES (?, ?, ?, 'running')";
        
        return $this->db->insert($sql, [$syncType, $startTime, $endTime]);
    }

    /**
     * 更新同步日志
     */
    public function updateSyncLog($logId, $data)
    {
        $fields = [];
        $values = [];
        
        foreach ($data as $field => $value) {
            $fields[] = "`{$field}` = ?";
            $values[] = $value;
        }
        
        $values[] = $logId;
        
        $sql = "UPDATE `zfb_sync_log` SET " . implode(', ', $fields) . " WHERE `id` = ?";
        
        return $this->db->update($sql, $values);
    }

    /**
     * 执行初始数据同步
     */
    public function performInitialSync($appId, $privateKey, $alipayPublicKey, $startDate = '2024-01-01', $endDate = '2025-05-31')
    {
        $startTime = microtime(true);
        $logId = $this->logSyncStart('initial', $startDate . ' 00:00:00', $endDate . ' 23:59:59');
        
        try {
            echo "开始执行初始数据同步...\n";
            echo "时间范围: {$startDate} 到 {$endDate}\n";
            echo "================================\n";
            
            $billFetcher = new BillFetcher($appId, $privateKey, $alipayPublicKey);
            
            // 获取历史数据
            $data = $billFetcher->fetchHistoricalData($startDate, $endDate);
            
            // 保存到数据库
            $saveResult = $billFetcher->saveDataToDatabase($data);
            
            $executionTime = round(microtime(true) - $startTime, 2);
            
            // 更新同步日志
            $this->updateSyncLog($logId, [
                'total_records' => count($data),
                'new_records' => $saveResult['inserted'],
                'updated_records' => $saveResult['updated'],
                'error_records' => $saveResult['errors'],
                'status' => 'success',
                'execution_time' => $executionTime
            ]);
            
            echo "================================\n";
            echo "✅ 初始同步完成！\n";
            echo "总记录数: " . count($data) . "\n";
            echo "新增: {$saveResult['inserted']} 条\n";
            echo "更新: {$saveResult['updated']} 条\n";
            echo "跳过: {$saveResult['skipped']} 条\n";
            echo "错误: {$saveResult['errors']} 条\n";
            echo "执行时间: {$executionTime} 秒\n";
            
            return true;
            
        } catch (Exception $e) {
            $executionTime = round(microtime(true) - $startTime, 2);
            
            $this->updateSyncLog($logId, [
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'execution_time' => $executionTime
            ]);
            
            echo "❌ 初始同步失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 执行增量数据同步
     */
    public function performIncrementalSync($appId, $privateKey, $alipayPublicKey, $minutes = 60, $webMode = false)
    {
        $startTime = microtime(true);
        $endTime = date('Y-m-d H:i:s');
        $syncStartTime = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));

        $logId = $this->logSyncStart('incremental', $syncStartTime, $endTime);

        try {
            if (!$webMode) {
                echo "[" . date('Y-m-d H:i:s') . "] 开始增量同步...\n";
            }

            $billFetcher = new BillFetcher($appId, $privateKey, $alipayPublicKey, $webMode);

            // 获取最新数据
            $data = $billFetcher->fetchLatestData($minutes);

            if (empty($data)) {
                if (!$webMode) {
                    echo "没有新数据需要同步\n";
                }

                $this->updateSyncLog($logId, [
                    'total_records' => 0,
                    'status' => 'success',
                    'execution_time' => round(microtime(true) - $startTime, 2)
                ]);

                return true;
            }

            // 保存到数据库
            $saveResult = $billFetcher->saveDataToDatabase($data);

            $executionTime = round(microtime(true) - $startTime, 2);

            // 更新同步日志
            $this->updateSyncLog($logId, [
                'total_records' => count($data),
                'new_records' => $saveResult['inserted'],
                'updated_records' => $saveResult['updated'],
                'error_records' => $saveResult['errors'],
                'status' => 'success',
                'execution_time' => $executionTime
            ]);

            if (!$webMode) {
                echo "增量同步完成: 新增 {$saveResult['inserted']} 条，更新 {$saveResult['updated']} 条\n";
            }

            return true;

        } catch (Exception $e) {
            $executionTime = round(microtime(true) - $startTime, 2);

            $this->updateSyncLog($logId, [
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'execution_time' => $executionTime
            ]);

            if (!$webMode) {
                echo "❌ 增量同步失败: " . $e->getMessage() . "\n";
            }
            return false;
        }
    }

    /**
     * 获取数据统计信息
     */
    public function getDataStatistics($webMode = false)
    {
        $stats = $this->zfbDataModel->getStatistics();

        if (!$webMode) {
            echo "=== 数据统计信息 ===\n";
            echo "总记录数: " . number_format($stats['total_records']) . "\n";
            echo "今日新增: " . number_format($stats['today_records']) . "\n";
            echo "成功交易: " . number_format($stats['success_count']) . " 笔\n";
            echo "成功金额: ¥" . number_format($stats['success_amount'], 2) . "\n";
            echo "最早记录: " . ($stats['earliest_time'] ?? 'N/A') . "\n";
            echo "最新记录: " . ($stats['latest_time'] ?? 'N/A') . "\n";
            echo "==================\n";
        }

        return $stats;
    }

    /**
     * 获取同步日志
     */
    public function getSyncLogs($limit = 10, $webMode = false)
    {
        $sql = "SELECT * FROM `zfb_sync_log` ORDER BY `created_at` DESC LIMIT ?";
        $stmt = $this->db->query($sql, [$limit]);
        $logs = $stmt->fetchAll();

        if (!$webMode) {
            echo "=== 最近同步日志 ===\n";
            foreach ($logs as $log) {
                $status = $log['status'] == 'success' ? '✅' : ($log['status'] == 'failed' ? '❌' : '🔄');
                echo "{$status} [{$log['created_at']}] {$log['sync_type']} - {$log['status']}\n";
                echo "   时间范围: {$log['start_time']} ~ {$log['end_time']}\n";
                echo "   记录数: {$log['total_records']} (新增: {$log['new_records']}, 更新: {$log['updated_records']}, 错误: {$log['error_records']})\n";
                echo "   执行时间: {$log['execution_time']} 秒\n";
                if (!empty($log['error_message'])) {
                    echo "   错误信息: {$log['error_message']}\n";
                }
                echo "   " . str_repeat('-', 50) . "\n";
            }
            echo "==================\n";
        }

        return $logs;
    }

    /**
     * 清理旧的同步日志
     */
    public function cleanOldSyncLogs($daysToKeep = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        $sql = "DELETE FROM `zfb_sync_log` WHERE `created_at` < ?";
        $affectedRows = $this->db->update($sql, [$cutoffDate]);
        
        echo "清理了 {$affectedRows} 条旧的同步日志记录\n";
        
        return $affectedRows;
    }

    /**
     * 检查系统状态
     */
    public function checkSystemStatus($webMode = false)
    {
        if (!$webMode) {
            echo "=== 系统状态检查 ===\n";
        }

        // 检查数据库连接
        try {
            $this->db->query("SELECT 1");
            if (!$webMode) {
                echo "✅ 数据库连接正常\n";
            }
        } catch (Exception $e) {
            if (!$webMode) {
                echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
            }
            return false;
        }

        // 检查表是否存在
        $tables = ['25_zfb_data', 'zfb_sync_log'];
        foreach ($tables as $table) {
            if ($this->db->tableExists($table)) {
                if (!$webMode) {
                    echo "✅ 表 {$table} 存在\n";
                }
            } else {
                if (!$webMode) {
                    echo "❌ 表 {$table} 不存在\n";
                }
                return false;
            }
        }

        // 检查最近的同步状态
        $sql = "SELECT * FROM `zfb_sync_log` ORDER BY `created_at` DESC LIMIT 1";
        $stmt = $this->db->query($sql);
        $lastSync = $stmt->fetch();

        if ($lastSync) {
            $timeDiff = time() - strtotime($lastSync['created_at']);
            if (!$webMode) {
                echo "最后同步: " . $lastSync['created_at'] . " (" . round($timeDiff / 60) . " 分钟前)\n";
                echo "同步状态: " . $lastSync['status'] . "\n";
            }
        } else {
            if (!$webMode) {
                echo "⚠️  尚未进行过数据同步\n";
            }
        }

        if (!$webMode) {
            echo "==================\n";
        }

        return true;
    }
}
