<?php

namespace AlipayBillQuery\Services;

use AlipayBillQuery\Models\ZfbData;
use Alipay\EasySDK\Kernel\Factory;
use Alipay\EasySDK\Kernel\Config;
use Exception;

/**
 * 账单数据获取服务
 *
 * 负责从支付宝API获取交易数据并转换为标准格式
 *
 * <AUTHOR> (照顾重病父母中)
 * @version 1.0
 */
class BillFetcher
{
    private $config;
    private $zfbDataModel;
    private $appId;
    private $privateKey;
    private $alipayPublicKey;
    private $webMode;

    public function __construct($appId, $privateKey, $alipayPublicKey, $webMode = false)
    {
        $this->appId = $appId;
        $this->privateKey = $privateKey;
        $this->alipayPublicKey = $alipayPublicKey;
        $this->webMode = $webMode;
        $this->zfbDataModel = new ZfbData();
        $this->initializeAlipayConfig();
    }

    /**
     * 初始化支付宝配置
     */
    private function initializeAlipayConfig()
    {
        $this->config = new Config();
        $this->config->protocol = 'https';
        $this->config->gatewayHost = 'openapi.alipay.com';
        $this->config->signType = 'RSA2';
        $this->config->appId = $this->appId;
        $this->config->merchantPrivateKey = $this->privateKey;
        $this->config->alipayPublicKey = $this->alipayPublicKey;
        
        Factory::setOptions($this->config);
    }

    /**
     * 获取指定时间范围的账单数据
     */
    public function fetchBillData($startTime, $endTime, $pageSize = 2000)
    {
        $allData = [];
        $pageNo = 1;
        $totalFetched = 0;

        do {
            if (!$this->webMode) {
                echo "正在获取第 {$pageNo} 页数据...\n";
            }

            $result = $this->queryBillPage($startTime, $endTime, $pageNo, $pageSize);

            if (!$result['success']) {
                throw new Exception("获取数据失败: " . $result['message']);
            }

            $billData = $result['data'];
            $pageData = $billData['detail_list'] ?? [];

            if (empty($pageData)) {
                break;
            }

            // 转换数据格式
            $convertedData = $this->convertBillData($pageData);
            $allData = array_merge($allData, $convertedData);

            $totalFetched += count($pageData);
            if (!$this->webMode) {
                echo "本页获取 " . count($pageData) . " 条记录，累计 {$totalFetched} 条\n";
            }

            // 检查是否还有更多数据
            $totalSize = intval($billData['total_size'] ?? 0);
            if ($totalFetched >= $totalSize) {
                break;
            }

            $pageNo++;
            
            // 添加延迟避免频率限制
            sleep(1);
            
        } while (true);

        return $allData;
    }

    /**
     * 查询单页账单数据
     */
    private function queryBillPage($startTime, $endTime, $pageNo, $pageSize)
    {
        try {
            $response = Factory::util()->generic()->execute(
                'alipay.data.bill.buy.query',
                [],
                [
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'page_no' => (string)$pageNo,
                    'page_size' => (string)$pageSize
                ]
            );

            $responseBody = $response->httpBody;
            $responseArray = json_decode($responseBody, true);
            
            if (isset($responseArray['alipay_data_bill_buy_query_response'])) {
                $billData = $responseArray['alipay_data_bill_buy_query_response'];
                
                if (isset($billData['code']) && $billData['code'] === '10000') {
                    return [
                        'success' => true,
                        'data' => $billData,
                        'message' => '查询成功'
                    ];
                } else {
                    return [
                        'success' => false,
                        'data' => null,
                        'message' => $billData['msg'] ?? '查询失败',
                        'error_code' => $billData['code'] ?? 'unknown',
                        'sub_code' => $billData['sub_code'] ?? '',
                        'sub_msg' => $billData['sub_msg'] ?? '',
                        'raw_response' => $billData
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'data' => null,
                    'message' => '响应格式异常'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'data' => null,
                'message' => '调用失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 转换账单数据格式
     */
    private function convertBillData($billList)
    {
        $convertedData = [];

        foreach ($billList as $bill) {
            $convertedData[] = [
                'alipay_order_no' => $bill['alipay_order_no'] ?? '',
                'merchant_order_no' => $bill['merchant_order_no'] ?? null,
                'gmt_create' => $this->formatDateTime($bill['gmt_create'] ?? null),
                'gmt_payment' => $this->formatDateTime($bill['gmt_payment'] ?? null),
                'goods_title' => $bill['goods_title'] ?? null,
                'total_amount' => floatval($bill['total_amount'] ?? 0),
                'other_account' => $bill['other_account'] ?? null,
                'trade_status' => $bill['trade_status'] ?? null,
                'trade_type' => $bill['trade_type'] ?? null,
                'store_name' => $bill['store_name'] ?? null,
                'buyer_user_id' => $bill['buyer_user_id'] ?? null,
                'seller_user_id' => $bill['seller_user_id'] ?? null,
                'trade_no' => $bill['trade_no'] ?? null,
                'subject' => $bill['subject'] ?? null,
                'body' => $bill['body'] ?? null,
                'receipt_amount' => isset($bill['receipt_amount']) ? floatval($bill['receipt_amount']) : null,
                'buyer_pay_amount' => isset($bill['buyer_pay_amount']) ? floatval($bill['buyer_pay_amount']) : null,
                'point_amount' => isset($bill['point_amount']) ? floatval($bill['point_amount']) : null,
                'invoice_amount' => isset($bill['invoice_amount']) ? floatval($bill['invoice_amount']) : null,
                'send_pay_date' => $this->formatDateTime($bill['send_pay_date'] ?? null),
                'alipay_store_id' => $bill['alipay_store_id'] ?? null,
                'store_id' => $bill['store_id'] ?? null,
                'terminal_id' => $bill['terminal_id'] ?? null,
                'fund_bill_list' => isset($bill['fund_bill_list']) ? json_encode($bill['fund_bill_list']) : null,
                'voucher_detail_list' => isset($bill['voucher_detail_list']) ? json_encode($bill['voucher_detail_list']) : null,
                'raw_data' => json_encode($bill),
                'data_source' => 'api'
            ];
        }

        return $convertedData;
    }

    /**
     * 格式化日期时间
     */
    private function formatDateTime($dateTime)
    {
        if (empty($dateTime)) {
            return null;
        }

        try {
            $dt = new \DateTime($dateTime);
            return $dt->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 获取最新数据（增量更新）
     */
    public function fetchLatestData($minutes = 60)
    {
        $endTime = date('Y-m-d H:i:s');
        $startTime = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));

        if (!$this->webMode) {
            echo "获取最近 {$minutes} 分钟的数据: {$startTime} 到 {$endTime}\n";
        }

        return $this->fetchBillData($startTime, $endTime);
    }

    /**
     * 获取历史数据
     */
    public function fetchHistoricalData($startDate, $endDate)
    {
        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $allData = [];

        // 按月分批获取，避免单次请求数据量过大
        while ($start <= $end) {
            $monthEnd = clone $start;
            $monthEnd->modify('last day of this month')->setTime(23, 59, 59);

            if ($monthEnd > $end) {
                $monthEnd = $end;
            }

            $startTime = $start->format('Y-m-d H:i:s');
            $endTime = $monthEnd->format('Y-m-d H:i:s');

            echo "获取 {$startTime} 到 {$endTime} 的数据\n";

            try {
                $monthData = $this->fetchBillData($startTime, $endTime);
                $allData = array_merge($allData, $monthData);
                echo "成功获取 " . count($monthData) . " 条记录\n";
            } catch (Exception $e) {
                echo "⚠️  跳过该月份: " . $e->getMessage() . "\n";
                // 继续处理下个月，不中断整个流程
            }

            // 移动到下个月
            $start->modify('first day of next month')->setTime(0, 0, 0);

            // 添加延迟
            sleep(2);
        }

        return $allData;
    }

    /**
     * 保存数据到数据库
     */
    public function saveDataToDatabase($data)
    {
        if (empty($data)) {
            return ['message' => '没有数据需要保存'];
        }

        if (!$this->webMode) {
            echo "开始保存 " . count($data) . " 条数据到数据库...\n";
        }

        $result = $this->zfbDataModel->batchInsert($data);

        if (!$this->webMode) {
            echo "保存完成: 新增 {$result['inserted']} 条，更新 {$result['updated']} 条，跳过 {$result['skipped']} 条，错误 {$result['errors']} 条\n";
        }

        return $result;
    }
}
